import React from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  Heading,
  Button,
  IconButton
} from 'common/components';
import colors from 'theme/foundations/colors';
import {
  AlertWarningIcon, CloseIcon, DoneIcon, ErrorIcon
} from 'assets/svg';

const CustomAlert = (props) => {
  const {
    open = false,
    size = 'md',
    close,
    haveClose = false,
    haveBodyClose = false,
    title = '',
    bodyTitle = '',
    message = '',
    variant = 'alert',
    forwardActionText = '',
    backwardActionText = '',
    actionBackward = () => { },
    actionForward = () => { },
    content,
    closeOnOverlayClick = true,
    closeOnEsc = true,
    forwardButtonProps = {},
    backwardButtonProps = {}
  } = props;

  return (
    <Modal isOpen={open} size={size} onClose={close} isCentered motionPreset="slideInBottom" closeOnOverlayClick={closeOnOverlayClick} closeOnEsc={closeOnEsc}>
      <ModalOverlay />
      <ModalContent className="alert-modal">
        <ModalHeader className="alert-header">
          {/* Header is hidden via CSS but keeping for backward compatibility */}
          <Heading as="h4" size="md" className="alert-head">
            {title}
            {haveClose
              && (
              <IconButton onClick={() => close()} variant="ghost" className="alert-close">
                <CloseIcon />
              </IconButton>
              )}
          </Heading>
        </ModalHeader>
        <ModalBody>
          {/* Close button positioned absolutely in the modal body */}
          {haveBodyClose && (
            <IconButton
              onClick={() => close()}
              variant="ghost"
              className="alert-close"
              aria-label="Close alert"
            >
              <CloseIcon />
            </IconButton>
          )}

          {content || (
            <>
              {/* Only render icon-cover if variant has an icon */}
              {(variant === 'success' || variant === 'error' || variant === 'warning') && (
                <div className="icon-cover">
                  <div className={`icon-wrapper ${variant}`}>
                    {variant === 'success' && <DoneIcon width="24" height="24" />}
                    {variant === 'error' && <ErrorIcon width="24" height="24" />}
                    {variant === 'warning' && <AlertWarningIcon width="24" height="24" />}
                  </div>
                </div>
              )}

              {/* Show title if provided */}
              {bodyTitle && (
                <Heading as="h3" size="md" className="alert-title" mb={2} color={colors.primary[500]} fontWeight="600">
                  {bodyTitle}
                </Heading>
              )}
              <div className="alert-message">
                {message}
              </div>
            </>
          )}
        </ModalBody>
        {(backwardActionText || forwardActionText) && (
        <ModalFooter className="alert-footer">
          {backwardActionText && (
            <Button
              variant="secondary_outline"
              size={{ base: 'sm', md: 'md' }}
              onClick={actionBackward}
              flex={{ base: '1', md: 'none' }}
              minW={{ base: 'auto', md: '120px' }}
              {...backwardButtonProps}
            >
              {backwardActionText}
            </Button>
          )}

          {forwardActionText && (
            <Button
              variant="secondary"
              size={{ base: 'sm', md: 'md' }}
              onClick={actionForward}
              flex={{ base: '1', md: 'none' }}
              minW={{ base: 'auto', md: '120px' }}
              {...forwardButtonProps}
            >
              {forwardActionText}
            </Button>
          )}
        </ModalFooter>
        )}
      </ModalContent>
    </Modal>
  );
};

export default CustomAlert;
