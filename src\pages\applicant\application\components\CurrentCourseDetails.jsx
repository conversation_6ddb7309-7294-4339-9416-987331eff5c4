import {
  Box, FormController, Grid, GridItem, t, TitledCard, Text
} from 'common/components';
import { FormLabel } from 'common/custom-components';
import React, { useEffect } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { actions as commonActions } from 'pages/common/slice';
import {
  ACADEMIC_YEAR_OPTIONS,
  CORRESPONDENCE,
  COURSE_MODE,
  INSTITUTION_TYPE_OPTIONS
} from '../constants';

const CurrentCourseDetails = () => {
  const {
    control,
    formState: { errors },
    setValue,
    getValues
  } = useFormContext();

  const courseMode = useWatch({ control, name: 'courseMode' });
  const dispatch = useDispatch();
  const prevCourseMode = React.useRef(getValues('courseMode'));

  useEffect(() => {
    if (courseMode === CORRESPONDENCE && prevCourseMode.current !== CORRESPONDENCE) {
      // Revert to REGULAR mode
      setValue('courseMode', 'REGULAR', {
        shouldValidate: true,
        shouldDirty: true
      });

      // Show warning toast
      dispatch(commonActions.setCustomToast({
        open: true,
        variant: 'warning',
        title: t('correspondenceNotAllowed')
      }));
    }
    prevCourseMode.current = courseMode;
  }, [courseMode, dispatch, setValue, t, getValues]);

  return (
    <TitledCard title={t('currentCourseDetails')} mt={8}>
      <Box p={6}>
        <Box mb={6}>
          <FormLabel label={t('institutionType')} required />
          <Box mt={2} mb={8} fontSize="sm" color="gray.500" />
          <FormController
            type="select"
            name="currentInstitutionType"
            control={control}
            errors={errors}
            options={INSTITUTION_TYPE_OPTIONS}
            optionKey="name"
            placeholder={t('currentInstitutionType')}
            label={t('pleaseSelectOption')}
            required
          />
        </Box>

        <Box mb={6}>
          <FormController
            type="text"
            label={t('institutionName')}
            name="currentInstitutionName"
            control={control}
            errors={errors}
            placeholder={t('enterField', { field: t('institutionName') })}
            required
          />
        </Box>

        <Box mb={6}>
          <FormLabel label={t('courseMode')} required />
          <Box mt={2} mb={2} fontSize="sm" color="gray.500">
            {t('pleaseSelectOption')}
          </Box>
          <FormController
            type="radio"
            name="courseMode"
            control={control}
            errors={errors}
            options={COURSE_MODE}
            optionKey="code"
            required
            direction="row"
          />
          {courseMode === CORRESPONDENCE && (
            <Text color="orange.500" mt={2} fontSize="sm" fontStyle="italic">
              {t('correspondenceModeNotAvailable')}
            </Text>
          )}
        </Box>

        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={[12, 6]}>
            <FormController
              type="select"
              label={t('academicYear')}
              name="academicYear"
              control={control}
              errors={errors}
              options={ACADEMIC_YEAR_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6]}>
            <FormController
              type="date"
              label={t('dateOfAdmission')}
              name="dateOfAdmission"
              control={control}
              errors={errors}
              required
              placeholder="DD/MM/YYYY"
              max={new Date().toISOString().split('T')[0]}
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );
};

export default CurrentCourseDetails;
