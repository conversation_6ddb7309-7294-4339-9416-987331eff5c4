import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Box, Text } from 'common/components';
import { Alert } from 'common/custom-components';
import { actions as commonActions } from 'pages/common/slice';
import { Checkbox } from '@chakra-ui/react';
import { t } from 'i18next';

const ConsentPopup = ({
  isOpen = false,
  onClose = () => {},
  onConsent = () => {},
  onCancel = null
}) => {
  const dispatch = useDispatch();

  const [isChecked, setIsChecked] = useState(false);
  const [showError, setShowError] = useState(false);

  const handleCheckboxChange = (e) => {
    setIsChecked(e.target.checked);
    if (showError && e.target.checked) {
      setShowError(false);
    }
  };

  const handleAgreeAndContinue = () => {
    if (!isChecked) {
      setShowError(true);
      return;
    }

    dispatch(commonActions.setAadhaarVerificationConsent(true));
    onConsent();
    onClose();
  };

  const handleCancel = () => {
    setIsChecked(false);
    setShowError(false);

    if (onCancel) {
      onCancel();
    } else {
      dispatch(commonActions.navigateTo({
        to: '/ui/applicant/dashboard'
      }));
    }

    onClose();
  };

  const customContent = (
    <Box textAlign="center" p={4}>
      {/* Consent Message */}
      <Text
        fontSize={{ base: '14px', md: '16px' }}
        color="gray.700"
        lineHeight="1.6"
        mb={6}
        textAlign="left"
      >
        {t('consentMessage')}
      </Text>

      {/* Checkbox */}
      <Box mb={4}>
        <Checkbox
          isChecked={isChecked}
          onChange={handleCheckboxChange}
          size="md"
        >
          <Text
            fontSize={{ base: '13px', md: '14px' }}
            color="gray.700"
            lineHeight="1.5"
            textAlign="left"
            ml={2}
          >
            {t('consentCheckboxLabel')}
          </Text>
        </Checkbox>

        {/* Error message */}
        {showError && (
          <Text
            color="red.500"
            fontSize="sm"
            mt={2}
            textAlign="left"
          >
            {t('consentRequired')}
          </Text>
        )}
      </Box>
    </Box>
  );

  return (
    <Alert
      open={isOpen}
      close={handleCancel}
      bodyTitle={t('consentTitle')}
      content={customContent}
      forwardActionText={t('agreeAndContinue')}
      backwardActionText={t('cancel')}
      actionForward={handleAgreeAndContinue}
      actionBackward={handleCancel}
      closeOnOverlayClick={false}
      closeOnEsc={false}
      haveBodyClose={false}
      size="lg"
    />
  );
};

export default ConsentPopup;
