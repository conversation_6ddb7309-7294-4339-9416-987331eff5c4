import React from 'react';
import {
  Box,
  FormController,
  Grid,
  GridItem,
  t,
  TitledCard
} from 'common/components';

const Declaration = ({ control, errors }) => {
  return (
    <TitledCard title={t('declaration')}>
      <Box p={{ base: 4, md: 6 }}>
        <Grid templateColumns="repeat(1, 1fr)" gap={{ base: 4, md: 6, lg: 8 }}>
          <GridItem>
            <FormController
              type="check"
              name="scholarshipDeclaration"
              control={control}
              errors={errors}
              size="md"
              label={t('scholarshipDeclarationText')}
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );
};

export default Declaration;
